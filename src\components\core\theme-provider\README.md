# Theme Providers

This directory contains theme provider components for the BMS Pulse application.

## Components

### ThemeProvider

The main theme provider used throughout the application. It uses the system default theme mode and allows users to toggle between light, dark, and system themes.

**Usage:**
```tsx
import { ThemeProvider } from '@/components/core/theme-provider/theme-provider';

export default function Layout({ children }) {
  return (
    <ThemeProvider>
      {children}
    </ThemeProvider>
  );
}
```

### DarkThemeProvider

A specialized theme provider that forces dark theme mode regardless of user preference. This is useful for specific pages or components that need to always display in dark mode.

**Usage:**
```tsx
import { DarkThemeProvider } from '@/components/core/theme-provider/dark-theme-provider';

export default function LandingPage() {
  return (
    <DarkThemeProvider>
      <YourComponent />
    </DarkThemeProvider>
  );
}
```

### ThemeProviderWithMode

A flexible theme provider that allows you to override the default theme mode with any specific mode (light, dark, or system).

**Usage:**
```tsx
import { ThemeProviderWithMode } from '@/components/core/theme-provider/dark-theme-provider';

// Force light theme
export function LightThemePage({ children }) {
  return (
    <ThemeProviderWithMode mode='light'>
      {children}
    </ThemeProviderWithMode>
  );
}

// Force dark theme
export function DarkThemePage({ children }) {
  return (
    <ThemeProviderWithMode mode='dark'>
      {children}
    </ThemeProviderWithMode>
  );
}

// Use system theme (same as default ThemeProvider)
export function SystemThemePage({ children }) {
  return (
    <ThemeProviderWithMode mode='system'>
      {children}
    </ThemeProviderWithMode>
  );
}
```

## Key Features

- **Page-specific theme overrides**: Override global theme preferences for specific pages
- **MUI CSS Variables compatibility**: Works seamlessly with MUI's CSS variables approach
- **Next.js App Router support**: Compatible with server components and App Router
- **Internationalization support**: Works with next-intl setup
- **Theme customization**: Supports all existing theme customization options
- **Non-intrusive**: Doesn't affect global theme switching functionality

## Implementation Details

All theme providers use the same underlying architecture:
- `EmotionCache` for CSS-in-JS styling
- `AppRouterCacheProvider` for Next.js App Router compatibility
- `MuiThemeProvider` with configurable `defaultMode`
- `CssBaseline` for consistent baseline styles

The key difference is the `defaultMode` prop passed to `MuiThemeProvider`:
- `ThemeProvider`: `defaultMode='system'`
- `DarkThemeProvider`: `defaultMode='dark'`
- `ThemeProviderWithMode`: `defaultMode={mode}` (configurable)

## Best Practices

1. **Use sparingly**: Only override theme mode when absolutely necessary for UX reasons
2. **Document usage**: Always document why a specific theme mode is being forced
3. **Test thoroughly**: Ensure the override works correctly across different devices and browsers
4. **Consider accessibility**: Make sure forced theme modes don't negatively impact accessibility
5. **Maintain consistency**: Use the same theme provider pattern across similar use cases
