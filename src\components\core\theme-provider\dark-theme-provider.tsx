'use client';

import { AppRouterCacheProvider } from '@mui/material-nextjs/v13-appRouter';
import CssBaseline from '@mui/material/CssBaseline';
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import * as React from 'react';

import { createTheme } from '@/styles/theme/create-theme';

import { Theme } from '@/styles/theme/types';
import EmotionCache from './emotion-cache';

export interface DarkThemeProviderProps {
  children: React.ReactNode;
  options?: Partial<Theme>;
}

export interface ThemeProviderWithModeProps {
  children: React.ReactNode;
  mode: 'light' | 'dark' | 'system';
  options?: Partial<Theme>;
}

/**
 * DarkThemeProvider - A specialized theme provider that forces dark theme mode
 *
 * This component is designed for pages that need to override the user's global theme
 * preference and always display in dark mode. It maintains compatibility with the
 * existing theme system while providing a page-specific theme override.
 *
 * Key features:
 * - Forces dark theme mode regardless of user preference
 * - Maintains compatibility with MUI's CSS variables approach
 * - Preserves all existing theme customizations
 * - Works with next-intl internationalization
 * - Compatible with App Router and server components
 */
export function DarkThemeProvider({ children, options }: DarkThemeProviderProps): React.JSX.Element {
  return (
    <ThemeProviderWithMode mode='dark' options={options}>
      {children}
    </ThemeProviderWithMode>
  );
}

/**
 * ThemeProviderWithMode - A flexible theme provider that allows overriding the default theme mode
 *
 * This component provides a more flexible approach to theme mode overrides, allowing you to
 * specify any mode (light, dark, or system) while maintaining all the benefits of the existing
 * theme system.
 *
 * @param mode - The theme mode to force ('light', 'dark', or 'system')
 * @param options - Optional theme customization options
 * @param children - Child components to render within the theme provider
 */
export function ThemeProviderWithMode({ children, mode, options }: ThemeProviderWithModeProps): React.JSX.Element {
  const theme = createTheme(options);

  return (
    <EmotionCache options={{ key: 'mui' }}>
      <AppRouterCacheProvider>
        <MuiThemeProvider theme={theme} defaultMode={mode}>
          <CssBaseline />
          {children}
        </MuiThemeProvider>
      </AppRouterCacheProvider>
    </EmotionCache>
  );
}
