import * as React from 'react';

import { DarkThemeProvider } from '@/components/core/theme-provider/dark-theme-provider';
import { LandingPage } from '@/components/landing/landing-page';

// Show the landing page for localized routes with forced dark theme
// This overrides the user's global theme preference for this page only
export default function Page(): React.JSX.Element {
  return (
    <DarkThemeProvider>
      <LandingPage />
    </DarkThemeProvider>
  );
}

// Alternative implementation using the flexible ThemeProviderWithMode:
// import { ThemeProviderWithMode } from '@/components/core/theme-provider/dark-theme-provider';
//
// export default function Page(): React.JSX.Element {
//   return (
//     <ThemeProviderWithMode mode='dark'>
//       <LandingPage />
//     </ThemeProviderWithMode>
//   );
// }
